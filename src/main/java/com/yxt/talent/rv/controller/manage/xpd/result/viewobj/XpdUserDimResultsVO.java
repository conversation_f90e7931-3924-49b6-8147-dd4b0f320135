package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "盘点人员维度结果")
public class XpdUserDimResultsVO extends UserBaseInfo {

    @Schema(description = "盘点人员维度结果")
    private List<XpdUserDimResultVO> userDimResults = new ArrayList<>();

}
