package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "盘点人员具体维度的结果")
public class XpdUserDimResultVO {
    @Schema(description = "维度id")
    private String sdDimId;

    @Schema(description = "维度分层id")
    private String gridLevelId;

    @I18nTranslate(codeField = "gridLevelNameI18n")
    @Schema(description = "维度分层名称")
    private String gridLevelName;

    @Schema(description = "维度分层名称国际化")
    private String gridLevelNameI18n;

    @Schema(description = "维度分层序号")
    private Integer gridLevelOrderIndex;

    @Schema(description = "分层值:比例或固定值。结果类型为<得分>时表示分数（包括绩效得分）,结果类型为<达标率>时表示达标率")
    private BigDecimal scoreValue;

    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;

    /**
     * 冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效
     */
    @Schema(description = "绩效活动评估结果(优秀/良好)id")
    private String perfResultId;

    @Schema(description = "第三维度所属分层颜色")
    private String thirdDimColor;

    public XpdUserDimResultVO(XpdResultUserDimPO xpdResultUserDimPO) {
        this.sdDimId = xpdResultUserDimPO.getSdDimId();
        this.gridLevelId = xpdResultUserDimPO.getGridLevelId();
        this.gridLevelName = xpdResultUserDimPO.getGridLevelName();
        this.gridLevelNameI18n = xpdResultUserDimPO.getGridLevelNameI18n();
        this.gridLevelOrderIndex = xpdResultUserDimPO.getGridLevelOrderIndex();
        this.thirdDimColor = xpdResultUserDimPO.getThirdDimColor();
        this.scoreValue = xpdResultUserDimPO.getScoreValue();
        this.qualifiedPtg = xpdResultUserDimPO.getQualifiedPtg();
    }
}
