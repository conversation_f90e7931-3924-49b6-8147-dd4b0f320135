package com.yxt.talent.rv.infrastructure.service.userinfo.assembler;

import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdDimCombGridResult;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * UserBaseInfoAssembler使用示例
 * 展示如何在各种场景下使用通用的Assembler
 * 
 * <AUTHOR> Agent
 */
@Component
public class UserBaseInfoAssemblerUsageExample {

    @Autowired
    private UserBaseInfoAssemblerHelper assemblerHelper;

    /**
     * 示例1：从UdpLiteUserPO创建UserBaseInfo对象
     */
    public UserBaseInfo createUserBaseInfo(UdpLiteUserPO udpUser) {
        return assemblerHelper.create(udpUser, UserBaseInfo.class);
    }

    /**
     * 示例2：从UdpLiteUserPO创建CaliMeetUserVO对象
     */
    public CaliMeetUserVO createCaliMeetUserVO(UdpLiteUserPO udpUser) {
        return assemblerHelper.create(udpUser, CaliMeetUserVO.class);
    }

    /**
     * 示例3：从UdpLiteUserPO创建XpdResultUserDimDetailVO对象
     */
    public XpdResultUserDimDetailVO createXpdResultUserDimDetailVO(UdpLiteUserPO udpUser) {
        return assemblerHelper.create(udpUser, XpdResultUserDimDetailVO.class);
    }

    /**
     * 示例4：从UdpLiteUserPO创建XpdDimCombGridResult对象
     */
    public XpdDimCombGridResult createXpdDimCombGridResult(UdpLiteUserPO udpUser) {
        return assemblerHelper.create(udpUser, XpdDimCombGridResult.class);
    }

    /**
     * 示例5：批量创建UserBaseInfo子类对象
     */
    public List<CaliMeetUserVO> createCaliMeetUserVOList(List<UdpLiteUserPO> udpUsers) {
        return assemblerHelper.createList(udpUsers, CaliMeetUserVO.class);
    }

    /**
     * 示例6：更新现有的UserBaseInfo子类对象
     */
    public void updateCaliMeetUserVO(UdpLiteUserPO udpUser, CaliMeetUserVO target) {
        assemblerHelper.update(udpUser, target);
    }

    /**
     * 示例7：转换UserBaseInfo子类对象（深拷贝）
     */
    public CaliMeetUserVO convertCaliMeetUserVO(CaliMeetUserVO source) {
        return assemblerHelper.convert(source);
    }

    /**
     * 示例8：批量转换UserBaseInfo子类对象
     */
    public List<CaliMeetUserVO> convertCaliMeetUserVOList(List<CaliMeetUserVO> sources) {
        return (List<CaliMeetUserVO>) assemblerHelper.convertList(sources);
    }
}
