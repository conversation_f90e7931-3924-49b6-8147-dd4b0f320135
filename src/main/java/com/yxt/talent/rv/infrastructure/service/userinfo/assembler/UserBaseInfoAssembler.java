package com.yxt.talent.rv.infrastructure.service.userinfo.assembler;

import com.yxt.talent.rv.infrastructure.config.mapstruct.BaseAssemblerConfig;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import jakarta.annotation.Nullable;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.Collection;

/**
 * 通用的UserBaseInfo及其子类的MapStruct转换器
 * 支持继承子类的复制，无需每个子类都写自己的Assembler
 * 
 * <AUTHOR> Agent
 */
@Mapper(config = BaseAssemblerConfig.class)
public interface UserBaseInfoAssembler {

    /**
     * 转换UserBaseInfo集合
     * 
     * @param source 源集合
     * @return 转换后的集合
     */
    @Nullable
    Collection<UserBaseInfo> convert(@Nullable Collection<UserBaseInfo> source);

    /**
     * 转换单个UserBaseInfo对象
     * 
     * @param source 源对象
     * @return 转换后的对象
     */
    @Nullable
    UserBaseInfo convert(@Nullable UserBaseInfo source);

    /**
     * 从UdpLiteUserPO更新UserBaseInfo对象
     * 支持所有继承UserBaseInfo的子类
     * 
     * @param source UdpLiteUserPO源对象
     * @param target UserBaseInfo目标对象（包括其子类）
     */
    @Mapping(target = "userId", source = "id")
    void update(@Nullable UdpLiteUserPO source, @MappingTarget UserBaseInfo target);

    /**
     * 从UdpLiteUserPO创建UserBaseInfo对象
     * 
     * @param source UdpLiteUserPO源对象
     * @return 新创建的UserBaseInfo对象
     */
    @Nullable
    @Mapping(target = "userId", source = "id")
    UserBaseInfo toUserBaseInfo(@Nullable UdpLiteUserPO source);

    /**
     * 从UdpLiteUserPO集合创建UserBaseInfo集合
     * 
     * @param sources UdpLiteUserPO源集合
     * @return 新创建的UserBaseInfo集合
     */
    @Nullable
    Collection<UserBaseInfo> toUserBaseInfos(@Nullable Collection<UdpLiteUserPO> sources);
}
