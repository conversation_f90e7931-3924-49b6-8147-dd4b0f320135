package com.yxt.talent.rv.infrastructure.service.userinfo.assembler;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import jakarta.annotation.Nullable;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.lang.reflect.Constructor;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserBaseInfo及其子类的转换助手类
 * 提供通用的转换方法，支持继承子类的复制
 * 
 * <AUTHOR> Agent
 */
@Component
public class UserBaseInfoAssemblerHelper {

    private static final UserBaseInfoAssembler ASSEMBLER = Mappers.getMapper(UserBaseInfoAssembler.class);

    /**
     * 从UdpLiteUserPO更新UserBaseInfo对象（包括其子类）
     * 
     * @param source UdpLiteUserPO源对象
     * @param target UserBaseInfo目标对象（包括其子类）
     */
    public void update(@Nullable UdpLiteUserPO source, @Nullable UserBaseInfo target) {
        if (source == null || target == null) {
            return;
        }
        ASSEMBLER.update(source, target);
    }

    /**
     * 从UdpLiteUserPO创建指定类型的UserBaseInfo子类对象
     * 
     * @param source UdpLiteUserPO源对象
     * @param targetClass 目标类型
     * @param <T> UserBaseInfo的子类型
     * @return 新创建的对象
     */
    @Nullable
    public <T extends UserBaseInfo> T create(@Nullable UdpLiteUserPO source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            // 尝试使用无参构造函数创建实例
            T instance = targetClass.getDeclaredConstructor().newInstance();
            ASSEMBLER.update(source, instance);
            return instance;
        } catch (Exception e) {
            // 如果无参构造函数失败，尝试使用UdpLiteUserPO参数的构造函数
            try {
                Constructor<T> constructor = targetClass.getDeclaredConstructor(UdpLiteUserPO.class);
                return constructor.newInstance(source);
            } catch (Exception ex) {
                throw new RuntimeException("无法创建 " + targetClass.getSimpleName() + " 实例", ex);
            }
        }
    }

    /**
     * 从UdpLiteUserPO集合创建指定类型的UserBaseInfo子类对象集合
     * 
     * @param sources UdpLiteUserPO源集合
     * @param targetClass 目标类型
     * @param <T> UserBaseInfo的子类型
     * @return 新创建的对象集合
     */
    @Nullable
    public <T extends UserBaseInfo> List<T> createList(@Nullable Collection<UdpLiteUserPO> sources, Class<T> targetClass) {
        if (sources == null || sources.isEmpty()) {
            return Collections.emptyList();
        }
        
        return sources.stream()
                .map(source -> create(source, targetClass))
                .collect(Collectors.toList());
    }

    /**
     * 转换UserBaseInfo对象（包括其子类）
     * 
     * @param source 源对象
     * @param <T> UserBaseInfo的子类型
     * @return 转换后的对象
     */
    @Nullable
    @SuppressWarnings("unchecked")
    public <T extends UserBaseInfo> T convert(@Nullable T source) {
        if (source == null) {
            return null;
        }
        return (T) ASSEMBLER.convert(source);
    }

    /**
     * 转换UserBaseInfo对象集合（包括其子类）
     * 
     * @param sources 源对象集合
     * @param <T> UserBaseInfo的子类型
     * @return 转换后的对象集合
     */
    @Nullable
    @SuppressWarnings("unchecked")
    public <T extends UserBaseInfo> Collection<T> convertList(@Nullable Collection<T> sources) {
        if (sources == null || sources.isEmpty()) {
            return Collections.emptyList();
        }
        return (Collection<T>) ASSEMBLER.convert((Collection<UserBaseInfo>) sources);
    }

    /**
     * 获取Assembler实例
     * 
     * @return UserBaseInfoAssembler实例
     */
    public UserBaseInfoAssembler getAssembler() {
        return ASSEMBLER;
    }
}
