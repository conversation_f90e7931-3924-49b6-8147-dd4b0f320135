# UserBaseInfo通用MapStruct Assembler

## 概述

这个重构提供了一个通用的MapStruct工具类，支持UserBaseInfo及其所有继承子类的复制，无需每个子类都写自己的Assembler。

## 核心组件

### 1. UserBaseInfoAssembler
- 通用的MapStruct接口
- 支持UserBaseInfo及其所有子类的转换
- 提供基础的转换方法

### 2. UserBaseInfoAssemblerHelper
- 助手类，提供更便捷的API
- 支持泛型，类型安全
- 自动处理实例创建和转换

## 使用方法

### 方法1：直接使用Assembler（推荐用于简单场景）

```java
// 获取Assembler实例
UserBaseInfoAssembler assembler = Mappers.getMapper(UserBaseInfoAssembler.class);

// 从UdpLiteUserPO更新UserBaseInfo对象
UdpLiteUserPO udpUser = ...;
UserBaseInfo userInfo = new UserBaseInfo();
assembler.update(udpUser, userInfo);

// 创建新的UserBaseInfo对象
UserBaseInfo newUserInfo = assembler.toUserBaseInfo(udpUser);
```

### 方法2：使用AssemblerHelper（推荐用于复杂场景）

```java
@Autowired
private UserBaseInfoAssemblerHelper assemblerHelper;

// 创建指定类型的子类对象
CaliMeetUserVO caliUser = assemblerHelper.create(udpUser, CaliMeetUserVO.class);
XpdResultUserDimDetailVO xpdUser = assemblerHelper.create(udpUser, XpdResultUserDimDetailVO.class);

// 批量创建
List<CaliMeetUserVO> caliUsers = assemblerHelper.createList(udpUsers, CaliMeetUserVO.class);

// 更新现有对象
assemblerHelper.update(udpUser, existingUserInfo);

// 转换对象（深拷贝）
CaliMeetUserVO copied = assemblerHelper.convert(originalCaliUser);
```

## 子类构造函数要求

为了支持通用转换，UserBaseInfo的子类需要满足以下条件之一：

1. **有无参构造函数**（推荐）
```java
@NoArgsConstructor
public class MyUserInfo extends UserBaseInfo {
    // 子类特有字段
}
```

2. **有接受UdpLiteUserPO参数的构造函数**
```java
public class MyUserInfo extends UserBaseInfo {
    public MyUserInfo(UdpLiteUserPO user) {
        super(user);
        // 子类特有的初始化逻辑
    }
}
```

## 迁移指南

### 旧的方式（需要移除）
```java
// 每个子类都需要自己的Assembler
@Mapper
public interface MyUserInfoAssembler {
    MyUserInfoAssembler INSTANCE = Mappers.getMapper(MyUserInfoAssembler.class);
    
    @Mapping(target = "userId", source = "id")
    void update(UdpLiteUserPO source, @MappingTarget MyUserInfo target);
}
```

### 新的方式（推荐）
```java
// 直接使用通用的AssemblerHelper
@Autowired
private UserBaseInfoAssemblerHelper assemblerHelper;

public MyUserInfo createFromUdpUser(UdpLiteUserPO udpUser) {
    return assemblerHelper.create(udpUser, MyUserInfo.class);
}
```

## 优势

1. **减少重复代码**：不需要为每个子类写Assembler
2. **类型安全**：使用泛型确保类型安全
3. **统一管理**：所有转换逻辑集中管理
4. **易于维护**：修改转换逻辑只需要在一个地方
5. **向后兼容**：现有的子类无需修改即可使用

## 注意事项

1. 确保子类有合适的构造函数
2. 如果子类有特殊的初始化逻辑，建议在构造函数中处理
3. 对于复杂的转换需求，可以继续使用专门的Assembler
4. 建议在Spring容器中使用AssemblerHelper，享受依赖注入的便利
