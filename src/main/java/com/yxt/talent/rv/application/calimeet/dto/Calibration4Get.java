package com.yxt.talent.rv.application.calimeet.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.common.Constants;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;

import java.util.Map;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import io.swagger.v3.oas.annotations.media.Schema;

@Getter
@Setter
@Schema(description = "校准任务详情返回")
public class Calibration4Get {


    @Schema(description = "主键;id主键")
    private String id;
    @Schema(description = "机构号;机构id")
    private String orgId;
    @Schema(description = "校准任务名称;名称")
    private String name;
    @Schema(description = "删除标识;0-未删除，1-已删除")
    private Integer deleted;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date createTime;
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2SECOND, timezone = Constants.STR_GMT8)
    private Date updateTime;
    @Schema(description = "创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description = "更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description = "组织形式")
    private String meetingType;
    @Schema(description = "开始时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date startTime;
    @Schema(description = "结束时间")
    @JsonFormat(pattern = Constants.SDF_YEAR2MINUTE, timezone = Constants.STR_GMT8)
    private Date endTime;
    @Schema(description = "组织人;按照业务需求,返回应用的实体字段")
    @JsonProperty("@organizator")
    private AmSlDrawer4RespDTO organizator;
    @Schema(description = "校准人;按照业务需求,返回应用的实体字段")
    @JsonProperty("@calibrator")
    private AmSlDrawer4RespDTO calibrator;
    @Schema(description = "校准方式")
    private String calibType;
    @Schema(description = "校准比例控制")
    private Integer rateControl;
    @Schema(description = "状态")
    private String meetStatus;
    @Schema(description = "校准人数")
    private Long calibUsers;
    @Schema(description = "会议记录")
    private String meetingRecord;

    @Schema(description = "扩展字段")
    private Map<String, Object> _spares = Maps.newHashMap();
}
