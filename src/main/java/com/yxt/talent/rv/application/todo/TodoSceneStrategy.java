package com.yxt.talent.rv.application.todo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4DeleteBatch;
import com.yxt.msgfacade.bean.Todo4DoneBatch;
import com.yxt.msgfacade.bean.Todo4ModifyBatch;
import com.yxt.msgfacade.service.TodoFacade;
import com.yxt.talent.rv.application.todo.TodoSceneEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 各种待办场景 请求入参的封装策略器
 *
 * @param <T> 封装新增待办的业务入参
 * @param <U> 封装更新待办的业务入参
 */
@Slf4j
@Component
public abstract class TodoSceneStrategy<T, U> {

    @Resource
    private TodoFacade todoFacade;

    public abstract TodoSceneEnum getTodoSceneEnum();

    public String getSceneCode() {
        return getTodoSceneEnum().getSceneCode();
    }

    protected abstract List<Todo4Create> convert2TodoCreates(String orgId, String opUserId, T bizParam);

    protected abstract Todo4ModifyBatch convert2Todo4ModifyBatch(String orgId, String opUserId, U bizParam);

    protected abstract Todo4ModifyBatch convert2TodoInfo4ModifyBatch(String orgId, String opUserId, U bizParam);

    protected Todo4DeleteBatch convert2Todo4DeleteBatch(String orgId, String opUserId, List<String> todoIds) {
        Todo4DeleteBatch rvt = new Todo4DeleteBatch();
        rvt.setOrgId(orgId);
        rvt.setOperateUserId(opUserId);
        rvt.setSceneCode(getSceneCode());
        rvt.setTodoIds(todoIds);
        return rvt;
    }

    protected Todo4DoneBatch convert2Todo4DoneBatch(String orgId, String opUserId,
            Map<String, String> todoId2UserIdMap) {
        Todo4DoneBatch rvt = new Todo4DoneBatch();
        rvt.setSceneCode(getSceneCode());
        rvt.setOrgId(orgId);
        rvt.setOperateUserId(opUserId);
        rvt.setTodos(todoId2UserIdMap.entrySet().stream().map(entry -> {
            Todo4DoneBatch.Todo todo = new Todo4DoneBatch.Todo();
            todo.setTodoId(entry.getKey());
            todo.setUserIds(ListUtil.toList(entry.getValue()));
            return todo;
        }).collect(Collectors.toList()));
        return rvt;
    }

    /**
     * 创建待办
     *
     * @param orgId    机构ID
     * @param opUserId 操作人ID
     * @param bizParam 待办创建入参
     */
    public void createTodos(String orgId, String opUserId, T bizParam) {
        List<Todo4Create> todo4Creates = convert2TodoCreates(orgId, opUserId, bizParam);
        if (CollUtil.isNotEmpty(todo4Creates)) {
            log.info("createTodos->req:{}", JSONUtil.toJsonStr(todo4Creates));
            todo4Creates.forEach(todoFacade::create);
        }
    }

    /**
     * 更新待办
     *
     * @param orgId    机构ID
     * @param opUserId 操作人ID
     * @param bizParam 待办更新入参
     */
    public void modifyTodos(String orgId, String opUserId, U bizParam) {
        Todo4ModifyBatch todo4ModifyBatch = convert2Todo4ModifyBatch(orgId, opUserId, bizParam);
        if (todo4ModifyBatch != null && CollUtil.isNotEmpty(todo4ModifyBatch.getTodos())) {
            log.info("modifyTodos->req:{}", JSONUtil.toJsonStr(todo4ModifyBatch));
            todoFacade.modifyBatch(todo4ModifyBatch);
        }
    }

    public void modifyTodoInfo(String orgId, String optUserId, U bizParam) {
        Todo4ModifyBatch todo4ModifyBatch = convert2TodoInfo4ModifyBatch(orgId, optUserId, bizParam);
        if (todo4ModifyBatch != null && CollUtil.isNotEmpty(todo4ModifyBatch.getTodos())) {
            log.info("modifyTodos->req:{}", JSONUtil.toJsonStr(todo4ModifyBatch));
            todoFacade.modifyBatch(todo4ModifyBatch);
        }
    }

    /**
     * 删除待办
     *
     * @param orgId    机构ID
     * @param opUserId 操作人ID
     * @param todoIds  待办消息ID
     */
    public void deleteTodos(String orgId, String opUserId, List<String> todoIds) {
        Todo4DeleteBatch todo4DeleteBatch = convert2Todo4DeleteBatch(orgId, opUserId, todoIds);
        if (todo4DeleteBatch != null && CollUtil.isNotEmpty(todo4DeleteBatch.getTodoIds())) {
            log.info("deleteTodos->req:{}", JSONUtil.toJsonStr(todo4DeleteBatch));
            todoFacade.deleteBatch(todo4DeleteBatch);
        }
    }

    /**
     * 完成待办
     *
     * @param orgId            机构ID
     * @param opUserId         操作人ID
     * @param todoId2UserIdMap 待办消息ID-完成待办用户ID map
     */
    public void doneTodos(String orgId, String opUserId, Map<String, String> todoId2UserIdMap) {
        Todo4DoneBatch todo4DoneBatch = convert2Todo4DoneBatch(orgId, opUserId, todoId2UserIdMap);
        if (todo4DoneBatch != null && CollUtil.isNotEmpty(todo4DoneBatch.getTodos())) {
            log.info("doneTodos->req:{}", JSONUtil.toJsonStr(todo4DoneBatch));
            todoFacade.doneBatch(todo4DoneBatch);
        }
    }
}
