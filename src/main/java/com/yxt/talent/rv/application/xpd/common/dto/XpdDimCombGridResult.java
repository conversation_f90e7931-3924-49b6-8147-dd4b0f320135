package com.yxt.talent.rv.application.xpd.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 人员宫格维度组视角下的结果
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class XpdDimCombGridResult extends UserBaseInfo {

    // 格子id
    private String cellId;

    // 格子编号
    private Integer cellIndex;

    @JsonProperty("xIndex")
    // x轴对应的层级编号，取自 rv_xpd_grid_level.order_index
    private Integer xIndex;

    @JsonProperty("yIndex")
    // y轴对应的层级编号，取自 rv_xpd_grid_level.order_index
    private Integer yIndex;

    @JsonIgnore
    private List<XpdUserDimResultVO> userDimResults = new ArrayList<>();

}
