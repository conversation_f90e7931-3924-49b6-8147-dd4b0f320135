<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdImportMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_import-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="import_type" property="importType" />
    <result column="score_total" property="scoreTotal" />
    <result column="create_user_id" property="createUserId" />
    <result column="update_user_id" property="updateUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="deleted" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, sd_dim_id, import_type, score_total, create_user_id, update_user_id, create_time,
    update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from rv_xpd_import
    where id = #{id}
  </select>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import (id, org_id, xpd_id, sd_dim_id, import_type, score_total, create_user_id, update_user_id,
    create_time, update_time, deleted)
    values (#{id}, #{orgId}, #{xpdId}, #{sdDimId}, #{importType}, #{scoreTotal}, #{createUserId}, #{updateUserId},
    #{createTime}, #{updateTime}, #{deleted})
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO">
    <!--@mbg.generated-->
    insert into rv_xpd_import
    (id, org_id, xpd_id, sd_dim_id, import_type, score_total, create_user_id, update_user_id, create_time,
    update_time, deleted)
    values
    (#{id}, #{orgId}, #{xpdId}, #{sdDimId}, #{importType}, #{scoreTotal}, #{createUserId}, #{updateUserId},
    #{createTime}, #{updateTime}, #{deleted})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    sd_dim_id = #{sdDimId},
    import_type = #{importType},
    score_total = #{scoreTotal},
    create_user_id = #{createUserId},
    update_user_id = #{updateUserId},
    create_time = #{createTime},
    update_time = #{updateTime},
    deleted = #{deleted}
  </insert>
    <select id="selectByXpdIdAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_xpd_import
        where
        xpd_id = #{xpdId,jdbcType=VARCHAR}
        AND org_id = #{orgId,jdbcType=VARCHAR}
        and deleted = 0
    </select>

  <select id="selectByXpdId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import
    where
    xpd_id = #{xpdId}
    AND org_id = #{orgId}
    <if test="importType != null">
      and import_type = #{importType}
    </if>
    and deleted = 0
  </select>

  <select id="selectPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import
    where
    xpd_id = #{xpdId,jdbcType=VARCHAR}
    AND org_id = #{orgId,jdbcType=VARCHAR}
    and deleted = 0
    order by create_time desc
  </select>
  <select id="findCountBySdDimId" resultType="java.lang.Integer">
    select
      count(*)
    from rv_xpd_import
    where
      xpd_id = #{xpdId,jdbcType=VARCHAR}
      AND org_id = #{orgId,jdbcType=VARCHAR}
      and deleted = 0
      and sd_dim_id = #{sdDimId,jdbcType=VARCHAR}
  </select>

  <select id="listBySdDimIdsAndImportType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and import_type = #{importType}
      and deleted = 0
    <choose>
      <when test="sdDimIds != null and sdDimIds.size() != 0">
        and sd_dim_id in
        <foreach collection="sdDimIds" item="sdDimId" open="(" close=")" separator=",">
          #{sdDimId}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 = 0
      </otherwise>
    </choose>
  </select>

  <select id="findByXpdIdAndImportType" resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO">
    select im.id,
           im.xpd_id,
           im.sd_dim_id,
           im.import_type,
           im.score_total
    from rv_xpd_import im
      <if test="mustXpdDim">
           inner join rv_xpd_dim d on im.sd_dim_id = d.sd_dim_id and im.xpd_id = d.xpd_id and d.deleted = 0
      </if>
    where im.org_id = #{orgId}
      and im.xpd_id = #{xpdId}
      and im.import_type = #{importType}
      and im.deleted = 0
  </select>

  <select id="selectDimIdByXpdIdAndOrgIdAndImportType" resultType="java.lang.String">
    select distinct a.sd_dim_id
    from rv_xpd_import a
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId}
      and a.import_type = #{importType}
      and a.deleted = 0
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_import
    where org_id = #{orgId}
    and deleted = 0
    and id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
  <update id="deleteByXpdId">
    update rv_xpd_import set deleted = 1,update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
  </update>

  <update id="deleteByXpdIdAndSdDimId">
    update rv_xpd_import set deleted = 1,update_time = now(), update_user_id = #{operator}
    where org_id = #{orgId} and xpd_id = #{xpdId}
          and  sd_dim_id in
    <foreach collection="dimIds" item="dimId" open="(" close=")" separator=",">
      #{dimId}
    </foreach>
         and  deleted = 0
  </update>

</mapper>