<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserDimMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_result_user_dim-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="user_id" property="userId" />
    <result column="sd_dim_id" property="sdDimId" />
    <result column="grid_level_id" property="gridLevelId" />
    <result column="score_value" property="scoreValue" />
    <result column="qualified_ptg" property="qualifiedPtg" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="perf_result_id" property="perfResultId" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg,
    deleted, create_user_id, create_time, update_user_id, update_time, calc_batch_no,
    perf_result_id, cali_flag, original_snap
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_dim
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_result_user_dim
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_dim (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value,
      qualified_ptg, deleted, create_user_id, create_time, update_user_id, update_time,
      calc_batch_no, perf_result_id, cali_flag, original_snap)
    values (#{id}, #{orgId}, #{xpdId}, #{userId}, #{sdDimId}, #{gridLevelId}, #{scoreValue},
      #{qualifiedPtg}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime},
      #{calcBatchNo}, #{perfResultId}, #{caliFlag}, #{originalSnap})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    <!--@mbg.generated-->
    update rv_xpd_result_user_dim
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      user_id = #{userId},
      sd_dim_id = #{sdDimId},
      grid_level_id = #{gridLevelId},
      score_value = #{scoreValue},
      qualified_ptg = #{qualifiedPtg},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calc_batch_no = #{calcBatchNo},
      perf_result_id = #{perfResultId},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_result_user_dim
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="sd_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdDimId}
        </foreach>
      </trim>
      <trim prefix="grid_level_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.gridLevelId}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreValue}
        </foreach>
      </trim>
      <trim prefix="qualified_ptg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.qualifiedPtg}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="perf_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfResultId}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg,
      deleted, create_user_id, create_time, update_user_id, update_time, calc_batch_no,
      perf_result_id, cali_flag, original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.sdDimId}, #{item.gridLevelId},
        #{item.scoreValue}, #{item.qualifiedPtg}, #{item.deleted}, #{item.createUserId},
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calcBatchNo},
        #{item.perfResultId}, #{item.caliFlag}, #{item.originalSnap})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg,
      deleted, create_user_id, create_time, update_user_id, update_time, calc_batch_no,
      perf_result_id, cali_flag, original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.sdDimId}, #{item.gridLevelId},
        #{item.scoreValue}, #{item.qualifiedPtg}, #{item.deleted}, #{item.createUserId},
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calcBatchNo},
        #{item.perfResultId}, #{item.caliFlag}, #{item.originalSnap})
    </foreach>
    on duplicate key update
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    user_id=values(user_id),
    sd_dim_id=values(sd_dim_id),
    grid_level_id=values(grid_level_id),
    score_value=values(score_value),
    qualified_ptg=values(qualified_ptg),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calc_batch_no=values(calc_batch_no),
    perf_result_id=values(perf_result_id),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_dim
    (id, org_id, xpd_id, user_id, sd_dim_id, grid_level_id, score_value, qualified_ptg,
      deleted, create_user_id, create_time, update_user_id, update_time, calc_batch_no,
      perf_result_id, cali_flag, original_snap)
    values
    (#{id}, #{orgId}, #{xpdId}, #{userId}, #{sdDimId}, #{gridLevelId}, #{scoreValue},
      #{qualifiedPtg}, #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime},
      #{calcBatchNo}, #{perfResultId}, #{caliFlag}, #{originalSnap})
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    user_id = #{userId}, 
    sd_dim_id = #{sdDimId},
    grid_level_id = #{gridLevelId}, 
    score_value = #{scoreValue}, 
    qualified_ptg = #{qualifiedPtg}, 
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime},
    calc_batch_no = #{calcBatchNo},
    perf_result_id = #{perfResultId},
    cali_flag = #{caliFlag},
    original_snap = #{originalSnap}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_dim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="sdDimId != null">
        sd_dim_id,
      </if>
      <if test="gridLevelId != null">
        grid_level_id,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="perfResultId != null">
        perf_result_id,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="sdDimId != null">
        #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        #{gridLevelId},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        #{qualifiedPtg},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="perfResultId != null">
        #{perfResultId},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="sdDimId != null">
        sd_dim_id = #{sdDimId},
      </if>
      <if test="gridLevelId != null">
        grid_level_id = #{gridLevelId},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg = #{qualifiedPtg},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="perfResultId != null">
        perf_result_id = #{perfResultId},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
    </trim>
  </insert>

  <select id="queryIgnoreDelByDimUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO">
    select id,user_id from rv_xpd_result_user_dim where org_id = #{orgId} and xpd_id = #{xpdId}
    and sd_dim_id = #{sdDimId} and user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryResultByDimUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.XpdUserDimResultDTO">
    select id,sd_dim_id,user_id,grid_level_id,score_value,qualified_ptg from rv_xpd_result_user_dim
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and sd_dim_id in
    <foreach close=")" collection="sdDimIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="batchUpdateResult">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_result_user_dim set
      grid_level_id = #{item.gridLevelId},
      score_value = #{item.scoreValue},
      qualified_ptg = #{item.qualifiedPtg},
      calc_batch_no = #{item.calcBatchNo},
      perf_result_id = #{item.perfResultId},
      deleted = #{item.deleted},
      update_user_id = #{item.updateUserId},
      update_time = #{item.updateTime} where id = #{item.id}
    </foreach>
  </update>

  <select id="listSortValue" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.DecimalPtgBean">
    select id,user_id,
    <choose>
      <when test="getScore">
        score_value as sort_value
      </when>
      <otherwise>
        <!--@ignoreSql-->
        qualified_ptg as sort_value
      </otherwise>
    </choose>
    from rv_xpd_result_user_dim
    where org_id = #{orgId} and xpd_id = #{xpdId} and sd_dim_id = #{sdDimId} and deleted = 0
  </select>

  <update id="batchUpdateLevelId">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_result_user_dim set grid_level_id = #{item.levelId} where id = #{item.id}
    </foreach>
  </update>

  <select id="queryUserLevelQty" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdUserLevelCountDto">
    select user_id,grid_level_id,count(id) as user_level_count
    from rv_xpd_result_user_dim where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and grid_level_id in
    <foreach close=")" collection="gridLevelIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    group by user_id,grid_level_id
  </select>

  <select id="queryUserDimLevel" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdUserDimLevelDto">
    select user_id,sd_dim_id,grid_level_id
    from rv_xpd_result_user_dim where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and sd_dim_id in
    <foreach close=")" collection="sdDimIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectDimLevelAgg" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdLevelAggVO">
    select b.id as levelId, b.level_name, b.level_name_i18n, b.order_index, count(distinct a.user_id) as userCnt
    from rv_xpd_grid_level           b

    left join rv_xpd_result_user_dim a
    on a.grid_level_id = b.id and a.org_id = b.org_id and b.deleted = 0 and b.xpd_id = a.xpd_id and a.deleted = 0
    <if test="query.targetId != null">
      and a.sd_dim_id = #{query.targetId}
    </if>

    left join udp_lite_user_sp c on c.org_id = a.org_id and c.user_id = a.user_id

    and exists(
      select 1
      from rv_activity_participation_member d
      join rv_xpd                           e on e.org_id = d.org_id and e.aom_prj_id = d.actv_id and e.deleted = 0
      where d.org_id = #{orgId}
      and e.id = #{xpdId}
      and d.deleted = 0
      and d.user_id = c.user_id
    )

    where b.org_id = #{orgId}
      and b.xpd_id = #{xpdId}
      and b.deleted = 0

    <include refid="auth_fragment" />

    <if test="query.levelIds != null and query.levelIds.size() != 0">
      and b.id in
      <foreach close=")" collection="query.levelIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    group by b.id, b.level_name, b.level_name_i18n, b.order_index
    order by b.order_index;
  </select>

  <!-- 通用权限 &amp;&amp; 搜索 -->
  <sql id="auth_fragment">
    <!--@ignoreSql-->
    <choose>
      <when test="query.emptyAuth">
        and 1 != 1
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0) and (query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and (c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        or c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0)">
        and c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="(query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <choose>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '') and (query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and (
          (
            <if test="query.kwType != null and query.kwType == 2">
              c.username like concat('%', #{query.escapedSearchKey}, '%')
            </if>
            <if test="query.kwType != null and query.kwType == 1">
              c.fullname like concat('%', #{query.escapedSearchKey}, '%')
            </if>
            <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
              (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
            </if>
          )
          or
          c.id in
          <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
            #{item}
          </foreach>
        )
      </when>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '')">
        <if test="query.kwType != null and query.kwType == 2">
          and c.username like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType != null and query.kwType == 1">
          and c.fullname like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
          and (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
        </if>
      </when>
      <when test="(query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <if test="query.posIds != null and query.posIds.size() != 0">
      and c.position_id in
      <foreach close=")" collection="query.posIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.gradeIds != null and query.gradeIds.size() != 0">
      and c.grade_id in
      <foreach close=")" collection="query.gradeIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.userStatus != null and query.userStatus != -1">
      <if test="query.userStatus == 2">
        and c.deleted = 1
      </if>
      <if test="query.userStatus != 2">
        and c.status = #{query.userStatus}
      </if>
    </if>

  </sql>

  <select id="selectUserGridOrderIndex" resultType="com.yxt.talent.rv.application.xpd.common.dto.UserGridOrderIndexDTO">
    select a.xpd_id
         , a.user_id
         , #{xSdDimId}                                          AS xSdDimId
         , #{ySdDimId}                                          AS ySdDimId
         , max(if(a.sd_dim_id = #{xSdDimId}, b.order_index, 0)) as xIndex
         , max(if(a.sd_dim_id = #{ySdDimId}, b.order_index, 0)) as yIndex
    from rv_xpd_result_user_dim a
    join rv_xpd_grid_level      b
         on a.grid_level_id = b.id and a.org_id = b.org_id and b.deleted = 0 and a.xpd_id = b.xpd_id
    join udp_lite_user_sp  c on a.user_id = c.id
    where a.org_id = #{orgId}
      and a.xpd_id = #{xpdId}
      and a.sd_dim_id in (#{xSdDimId}, #{ySdDimId})
      and a.deleted = 0
      <include refid="auth_fragment" />
      and exists (
        select 1 from rv_activity_participation_member f
        join rv_xpd g on g.org_id = f.org_id and g.aom_prj_id = f.actv_id and g.deleted = 0
        where f.org_id = #{orgId}
        and g.id = #{xpdId}
        and f.deleted = 0
        and f.user_id = a.user_id
      )

        <if test="query.cellIds != null and query.cellIds.size() != 0">
            <choose>
              <when test="allUserIds != null and allUserIds.size() != 0">
                AND a.user_id IN
                <foreach collection="allUserIds" item="userId" open="(" separator="," close=")">
                  #{userId}
                </foreach>
              </when>
              <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
              </otherwise>
            </choose>
        </if>

    group by a.xpd_id, a.user_id
    having xIndex &gt; 0 and yIndex &gt; 0
    order by xIndex, yIndex
  </select>

  <select id="findByXpdId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from rv_xpd_result_user_dim
      where xpd_id = #{xpdId}
      and deleted = 0
      and org_id = #{orgId}
  </select>

  <select id="selectUserDimResult" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO">
    WITH
    <!--CTE for user dimension results with row number-->
    user_dim_rn AS (
        SELECT
            ud.user_id,
            ud.org_id,
            ud.xpd_id,
            ud.sd_dim_id,
            ud.score_value,
            ud.qualified_ptg,
            ud.grid_level_id,
            ud.perf_result_id,
            ud.id,
            ROW_NUMBER() OVER (PARTITION BY ud.org_id, ud.xpd_id, ud.user_id, ud.sd_dim_id ORDER BY ud.create_time DESC) AS rn
        FROM
            rv_xpd_result_user_dim ud
        WHERE
            ud.org_id = #{orgId}
            AND ud.xpd_id = #{xpdId}
            AND ud.deleted = 0
    ),
    <!--CTE for user dimension results, filtering by row number = 1-->
    user_dim_filtered AS (
        SELECT *
        FROM user_dim_rn
        WHERE rn = 1
    ),

    <if test="query.includeSub">
    <!--CTE for user indicator results with row number-->
    user_indicator_rn AS (
        SELECT
            ui.user_id,
            ui.org_id,
            ui.xpd_id,
            ui.result_dim_id,
            ui.sd_indicator_id,
            ui.score_value,
            ui.qualified,
            ui.perf_summary,
            ui.id,
            ROW_NUMBER() OVER (PARTITION BY ui.org_id, ui.xpd_id, ui.user_id, ui.sd_indicator_id ORDER BY ui.create_time DESC) AS rn
        FROM
            rv_xpd_result_user_indicator ui
        WHERE
            ui.org_id = #{orgId}
            AND ui.xpd_id = #{xpdId}
            AND ui.deleted = 0
    ),
    <!--CTE for user indicator results, filtering by row number = 1-->
    user_indicator_filtered AS (
        SELECT *
        FROM user_indicator_rn
        WHERE rn = 1
    ),
    </if>

    <!--CTE for performance results-->
    perf_results AS (
        SELECT
            g.id AS perf_result_id,
            g.result_name AS perf_result_name
        FROM
            rv_activity t
            JOIN rv_xpd j ON j.org_id = t.org_id AND j.aom_prj_id = t.source_id AND j.deleted = 0 AND j.id = #{xpdId}
            JOIN rv_activity_perf p ON t.id = p.id AND p.deleted = 0
            LEFT JOIN rv_activity_perf_result_conf g ON g.actv_perf_id = p.id AND g.deleted = 0
        WHERE
            t.org_id = #{orgId}
            AND t.deleted = 0
    ),

    <!--CTE for final user dimension results-->
    user_dim_results AS (
        SELECT
            m.user_id,
            m.org_id,
            m.xpd_id,
            m.sd_dim_id,
            n.result_type,
            m.score_value,
            m.qualified_ptg,
            m.grid_level_id AS levelId,
            o.order_index AS levelIndex,
            o.level_name AS levelName,
            o.level_name_i18n AS levelNameI18n,
            CASE
                <!--计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算 -->
                <!-- 结果类型:绩效评估结果，0-分值 1-达标率 -->
                WHEN n.calc_type = 2 THEN h.perf_result_name
                WHEN n.calc_type = 3 THEN FORMAT(m.score_value, 2)
                WHEN n.result_type = 0 THEN FORMAT(m.score_value, 2)
                WHEN n.result_type = 1 THEN CONCAT(FORMAT(m.qualified_ptg, 2), '%')
            END AS result
            <if test="query.includeSub">
              , p.sd_indicator_id AS subId
              , '指标id关联人才标准数据库' AS subName
              , CASE
                  WHEN p.perf_summary != '' THEN p.perf_summary
                  <!-- 按照维度导入结果时，没有维度规则数据，也没有指标和达标率 -->
                  WHEN n.id IS NULL THEN ''
                  WHEN n.result_type = 0 THEN FORMAT(p.score_value, 2)
                  WHEN n.result_type = 1 THEN IF(p.qualified = 1, '达标', IF(p.qualified = 0, '未达标', '--'))
                END AS subResult
            </if>
        FROM
            user_dim_filtered m
            JOIN rv_xpd_grid_level o ON o.id = m.grid_level_id AND o.org_id = m.org_id AND o.deleted = 0
            LEFT JOIN rv_xpd_dim_rule n ON n.org_id = m.org_id AND n.xpd_id = m.xpd_id AND n.sd_dim_id = m.sd_dim_id AND n.deleted = 0
            LEFT JOIN perf_results h ON h.perf_result_id = m.perf_result_id
            <if test="query.includeSub">
              LEFT JOIN user_indicator_filtered p ON p.xpd_id = m.xpd_id AND p.user_id = m.user_id AND p.result_dim_id = m.id
            </if>
        WHERE
            m.org_id = #{orgId}

            <if test="query.targetId != null">
              AND m.sd_dim_id = #{query.targetId}
            </if>
    )
    SELECT
        c.user_id,
        c.fullname,
        c.username,
        IF(c.deleted = 1, 2, c.status) AS status,
        c.dept_name,
        c.dept_id,
        c.position_id,
        c.position_name,
        c.grade_id,
        c.grade_name,
        dd.sd_dim_id,
        dd.levelId,
        dd.levelName,
        dd.levelNameI18n,
        dd.result_type,
        dd.result
        <if test="query.includeSub">
        , dd.subId
        , dd.subName
        , dd.subResult
        </if>
    FROM
        rv_activity_participation_member a
        JOIN rv_xpd b ON b.org_id = a.org_id AND b.aom_prj_id = a.actv_id AND b.deleted = 0
        JOIN udp_lite_user_sp c ON c.org_id = #{orgId} AND c.user_id = a.user_id
        LEFT JOIN user_dim_results dd ON dd.xpd_id = b.id AND dd.user_id = a.user_id
    WHERE
        b.org_id = #{orgId}
        AND b.id = #{xpdId}
        AND a.deleted = 0
        <if test="query.levelIds != null and query.levelIds.size() != 0">
        AND dd.levelId IN
            <foreach close=")" collection="query.levelIds" item="levelId" open="(" separator=",">
                #{levelId}
            </foreach>
        </if>
        <include refid="auth_fragment" />
    ORDER BY
        IF(dd.sd_dim_id IS NULL, 1, 0),
        dd.sd_dim_id,
        IF(dd.levelIndex IS NULL, 1, 0),
        dd.levelIndex DESC,
        IF(dd.result_type IS NULL, 1, 0),
        IF(IF(dd.result_type = 0, dd.score_value, dd.qualified_ptg) IS NULL, 1, 0),
        IF(dd.result_type = 0, dd.score_value, dd.qualified_ptg) DESC
        <if test="query.includeSub">
        , dd.subId
        </if>
  </select>

  <select id="selectDeptUserDimLevel" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdDeptUserDimResultDTO">
    WITH dept_user_cnt AS (
        SELECT h.id AS deptId
             , h.NAME AS deptName
             , h.parent_id AS parentId
             , COUNT(DISTINCT k.user_id) AS userCnt
        FROM udp_dept h
        JOIN udp_dept_relation i ON h.id = i.parent_id AND i.dept_type = 1 AND h.org_id = i.org_id
        JOIN udp_lite_user_sp c ON c.dept_id = i.dept_id AND c.org_id = h.org_id
        JOIN rv_activity_participation_member k ON k.user_id = c.user_id AND k.org_id = c.org_id AND k.deleted = 0
        JOIN rv_xpd l ON l.org_id = k.org_id AND l.aom_prj_id = k.actv_id AND l.id = #{xpdId} AND l.deleted = 0
        WHERE h.org_id = #{orgId}
          AND h.deleted = 0

        <include refid="auth_fragment" />

        GROUP BY h.id, h.NAME, h.parent_id
        HAVING userCnt &gt; 0
    ),
    dept_level_user_cnt AS (
        SELECT a.id AS deptId
             , d.id AS levelId
             , d.level_name AS levelName
             , d.level_name_i18n AS levelNameI18n
             , d.order_index AS levelOrderIndex
             , COUNT(DISTINCT e.user_id) AS levelUserCnt
        FROM udp_dept a
        JOIN udp_dept_relation b ON a.id = b.parent_id AND b.dept_type = 1 AND a.org_id = b.org_id
        JOIN udp_lite_user_sp c ON c.dept_id = b.dept_id AND c.org_id = a.org_id
        JOIN rv_xpd_grid_level d ON d.org_id = a.org_id AND d.xpd_id = #{xpdId} AND d.deleted = 0
        LEFT JOIN rv_xpd_result_user_dim e ON e.user_id = c.user_id AND e.xpd_id = #{xpdId} AND e.grid_level_id = d.id AND
                                              e.sd_dim_id = #{query.targetId} AND e.deleted = 0
        WHERE a.org_id = #{orgId}
          AND a.deleted = 0
          AND EXISTS (
              SELECT 1 FROM rv_activity_participation_member f
              JOIN rv_xpd g ON g.org_id = f.org_id AND g.aom_prj_id = f.actv_id AND g.deleted = 0
              WHERE f.org_id = #{orgId}
                AND g.id = #{xpdId}
                AND f.deleted = 0
                AND f.user_id = c.user_id
          )

        <if test="query.levelIds != null and query.levelIds.size() != 0">
          AND d.id IN
          <foreach close=")" collection="query.levelIds" item="levelId" open="(" separator=",">
            #{levelId}
          </foreach>
        </if>

        <include refid="auth_fragment" />
        GROUP BY a.id, d.id, d.level_name, d.level_name_i18n, d.order_index
    )
    SELECT e.deptId
         , e.deptName
         , e.parentId
         , e.userCnt
         , g.levelId
         , g.levelName
         , g.levelNameI18n
         , g.levelOrderIndex
         , g.levelUserCnt
    FROM dept_user_cnt e
    LEFT JOIN dept_level_user_cnt g ON g.deptId = e.deptId
</select>

  <select id="getDimCombTableResult" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombTableResultVO">
    select c.username                     as username
         , c.user_id                      as userId
         , c.fullname                     as fullname
         , if(c.deleted = 1, 2, c.status) as status
         , c.dept_id                      as deptId
         , c.dept_name                    as deptName
         , c.position_id                  as positionId
         , c.position_name                as positionName
         , c.grade_id                     as gradeId
         , c.grade_name                   as gradeName
         , g.cell_name                    as cellName
         , g.cell_name_i18n               as cellNameI18n
         , x_dim_result.xSdDimName        as xSdDimName
         , x_dim_result.level_id          as xDimLevelId
         , x_dim_result.level_index       as xDimLevelIndex
         , x_dim_result.level_name        as xDimLevelName
         , x_dim_result.level_name_i18n   as xDimLevelNameI18n
         , y_dim_result.ySdDimName        as ySdDimName
         , y_dim_result.level_id          as yDimLevelId
         , y_dim_result.level_index       as yDimLevelIndex
         , y_dim_result.level_name        as yDimLevelName
         , y_dim_result.level_name_i18n   as yDimLevelNameI18n
    from rv_activity_participation_member a
    join       rv_xpd                     b on b.org_id = a.org_id and b.aom_prj_id = a.actv_id and b.deleted = 0
    join       udp_lite_user_sp           c on c.user_id = a.user_id
    left join  (
                 select rud.user_id
                      , '跨sd库查询x轴维度名称' as xSdDimName
                      , gl.id                   as level_id
                      , gl.order_index          as level_index
                      , gl.level_name           as level_name
                      , gl.level_name_i18n      as level_name_i18n
                      , gl.order_index          as order_index
                 from rv_xpd_result_user_dim rud
                 left join rv_xpd_grid_level gl on rud.grid_level_id = gl.id
                 where rud.sd_dim_id = #{xSdDimId}
                   and rud.org_id = #{orgId}
                   and rud.xpd_id = #{xpdId}
                   and rud.deleted = 0
                 ) as                     x_dim_result on c.user_id = x_dim_result.user_id
    inner join (
                 select rud.user_id
                      , '跨sd库查询y轴维度名称' as ySdDimName
                      , gl.id                   as level_id
                      , gl.order_index          as level_index
                      , gl.level_name           as level_name
                      , gl.level_name_i18n      as level_name_i18n
                      , gl.order_index          as order_index
                 from rv_xpd_result_user_dim rud
                 left join rv_xpd_grid_level gl on rud.grid_level_id = gl.id
                 where rud.sd_dim_id = #{ySdDimId}
                   and rud.org_id = #{orgId}
                   and rud.xpd_id = #{xpdId}
                   and rud.deleted = 0
                 ) as                     y_dim_result on c.user_id = y_dim_result.user_id
    join       rv_xpd_grid_cell           g on c.org_id = g.org_id and b.id = g.xpd_id and g.deleted = 0 and
                                               ((g.dim_comb_id = '') or (g.dim_comb_id = #{query.targetId})) and
                                               g.x_index = x_dim_result.order_index and
                                               g.y_index = y_dim_result.order_index
    where a.org_id = #{orgId}
      and b.id = #{xpdId}
      and a.deleted = 0

    <if test="query.cellIds != null and query.cellIds.size() != 0">
      and g.id in
      <foreach collection="query.cellIds" item="itemId" open="(" separator="," close=")">
        #{itemId}
      </foreach>
    </if>

    <include refid="auth_fragment"/>

    order by c.fullname
  </select>

  <select id="getDimCombTableResultAll"
          resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombTableResultVO">
    select c.username                     as username
    , c.user_id                      as userId
    , c.fullname                     as fullname
    , if(c.deleted = 1, 2, c.status) as status
    , c.dept_id                      as deptId
    , c.dept_name                    as deptName
    , c.position_id                  as positionId
    , c.position_name                as positionName
    , c.grade_id                     as gradeId
    , c.grade_name                   as gradeName
    , g.cell_name                    as cellName
    , g.cell_name_i18n               as cellNameI18n
    , x_dim_result.xSdDimName        as xSdDimName
    , x_dim_result.level_id          as xDimLevelId
    , x_dim_result.level_index       as xDimLevelIndex
    , x_dim_result.level_name        as xDimLevelName
    , x_dim_result.level_name_i18n   as xDimLevelNameI18n
    , y_dim_result.ySdDimName        as ySdDimName
    , y_dim_result.level_id          as yDimLevelId
    , y_dim_result.level_index       as yDimLevelIndex
    , y_dim_result.level_name        as yDimLevelName
    , y_dim_result.level_name_i18n   as yDimLevelNameI18n
    from rv_activity_participation_member a
    join       rv_xpd                     b on b.org_id = a.org_id and b.aom_prj_id = a.actv_id and b.deleted = 0
    join       udp_lite_user_sp           c on c.user_id = a.user_id
    left join  (
    select rud.user_id
    , '跨sd库查询x轴维度名称' as xSdDimName
    , gl.id                   as level_id
    , gl.order_index          as level_index
    , gl.level_name           as level_name
    , gl.level_name_i18n      as level_name_i18n
    , gl.order_index          as order_index
    from rv_xpd_result_user_dim rud
    left join rv_xpd_grid_level gl on rud.grid_level_id = gl.id
    where rud.sd_dim_id = #{xSdDimId}
    and rud.org_id = #{orgId}
    and rud.xpd_id = #{xpdId}
    and rud.deleted = 0
    ) as                     x_dim_result on c.user_id = x_dim_result.user_id
    inner join (
    select rud.user_id
    , '跨sd库查询y轴维度名称' as ySdDimName
    , gl.id                   as level_id
    , gl.order_index          as level_index
    , gl.level_name           as level_name
    , gl.level_name_i18n      as level_name_i18n
    , gl.order_index          as order_index
    from rv_xpd_result_user_dim rud
    left join rv_xpd_grid_level gl on rud.grid_level_id = gl.id
    where rud.sd_dim_id = #{ySdDimId}
    and rud.org_id = #{orgId}
    and rud.xpd_id = #{xpdId}
    and rud.deleted = 0
    ) as                     y_dim_result on c.user_id = y_dim_result.user_id
    join       rv_xpd_grid_cell           g on c.org_id = g.org_id and b.id = g.xpd_id and g.deleted = 0 and
    ((g.dim_comb_id = '') or (g.dim_comb_id = #{query.targetId})) and
    g.x_index = x_dim_result.order_index and
    g.y_index = y_dim_result.order_index
    where a.org_id = #{orgId}
    and b.id = #{xpdId}
    and a.deleted = 0

    <if test="query.cellIds != null and query.cellIds.size() != 0">
      and g.id in
      <foreach close=")" collection="query.cellIds" item="itemId" open="(" separator=",">
        #{itemId}
      </foreach>
    </if>

    <include refid="auth_fragment" />

    order by c.fullname
  </select>

  <select id="getDimCombGridResult" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdDimCombGridResult">
    with user_dim_levels as (
        select a.xpd_id
             , c.id
             , c.username
             , c.user_id
             , c.fullname
             , if(c.deleted = 1, 2, c.status)                        as status
             , c.dept_id
             , c.dept_name
             , c.position_id
             , c.position_name
             , c.grade_id
             , c.grade_name
             , c.img_url
             , max(if(a.sd_dim_id = #{xSdDimId}, b.order_index, -1)) as x_index
             , max(if(a.sd_dim_id = #{ySdDimId}, b.order_index, -1)) as y_index
        from rv_xpd_result_user_dim  a
        inner join udp_lite_user_sp  c on a.user_id = c.user_id
        inner join rv_xpd_grid_level b
                   on a.grid_level_id = b.id and a.org_id = b.org_id and b.deleted = 0 and a.xpd_id = b.xpd_id
        where a.org_id = #{orgId}
          and a.xpd_id = #{xpdId}
          and a.sd_dim_id in (#{xSdDimId}, #{ySdDimId})
          and a.deleted = 0
          and exists (
            select 1 from rv_activity_participation_member f
            join rv_xpd g on g.org_id = f.org_id and g.aom_prj_id = f.actv_id and g.deleted = 0
            where f.org_id = #{orgId}
              and g.id = #{xpdId}
              and f.deleted = 0
              and f.user_id = c.user_id
          )

        <include refid="auth_fragment" />

        group by a.xpd_id, c.id
        having x_index &gt; 0
        and y_index &gt; 0
    )
    select u.username
    , u.user_id as userId
    , u.fullname
    , u.status
    , u.dept_id as deptId
    , u.dept_name as deptName
    , u.position_id as positionId
    , u.position_name as positionName
    , u.grade_id as gradeId
    , u.grade_name as gradeName
    , u.x_index as xIndex
    , u.y_index as yIndex
    , d.id as cellId
    , d.cell_index as cellIndex
    from user_dim_levels u
    inner join rv_xpd_grid_cell d
    on d.xpd_id = u.xpd_id and d.deleted = 0 and (d.dim_comb_id = #{query.targetId} or d.dim_comb_id = '') and
    d.x_index = u.x_index and d.y_index = u.y_index

    <if test="query.cellIds != null and query.cellIds.size() != 0">
      and d.id in
      <foreach close=")" collection="query.cellIds" item="itemId" open="(" separator=",">
        #{itemId}
      </foreach>
    </if>

    ORDER BY u.x_index, u.y_index
  </select>

  <select id="findByXpdIdAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_dim
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and user_id = #{userId}
    and deleted = 0
  </select>

  <select id="getUserDimDetail" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimLevelVO">
    select a.sd_dim_id, a.grid_level_id, b.order_index
    from (
      select sd_dim_id
           , grid_level_id
           , row_number() over (partition by user_id, sd_dim_id order by create_time desc) as rn
      from rv_xpd_result_user_dim
      where org_id = #{orgId}
        and xpd_id = #{xpdId}
        and user_id = #{userId}
        and deleted = 0
      )                         as a
    join rv_xpd_grid_level as b on a.grid_level_id = b.id and b.deleted = 0
    where a.rn = 1
  </select>

  <select id="getDimCombDeptResult" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdDimCombDeptResultDTO">
     <!--查询每个部门下参与盘点的人数（包括后代子孙部门的人数）-->
     with DeptUserCount as (
        select h.id as deptId, h.name as deptName, h.parent_id as parentId, count(distinct k.user_id) as userCnt
        from udp_dept                         h
        join udp_dept_relation                i on h.id = i.parent_id and i.dept_type = 1 and h.org_id = i.org_id
        join udp_lite_user_sp                 c on c.dept_id = i.dept_id and c.org_id = h.org_id
        join rv_activity_participation_member k on k.user_id = c.user_id and k.org_id = c.org_id and k.deleted = 0
        join rv_xpd                           l on l.org_id = k.org_id and l.aom_prj_id = k.actv_id and l.id = #{xpdId} and l.deleted = 0
        where h.org_id = #{orgId}
          and h.deleted = 0

        <include refid="auth_fragment" />

        group by h.id, h.name, h.parent_id
        having userCnt &gt; 0
    )

    <!--用户在指定xy轴上的落位下标-->
    , UserXYIndex as (
        select c.user_id
             , c.dept_id
             , max(if(r.sd_dim_id = #{xSdDimId}, t.order_index, -1)) as xIndex
             , max(if(r.sd_dim_id = #{ySdDimId}, t.order_index, -1)) as yIndex
        from rv_xpd_result_user_dim r
        join      udp_lite_user_sp  c on c.user_id = r.user_id
        left join rv_xpd_grid_level t on r.grid_level_id = t.id and t.xpd_id = r.xpd_id and t.deleted = 0
        where r.xpd_id = #{xpdId}
          and r.org_id = #{orgId}
          and r.sd_dim_id in (#{xSdDimId}, #{ySdDimId})
          and r.deleted = 0
          and exists(
            select 1
            from rv_activity_participation_member f
            join rv_xpd                           g on g.org_id = f.org_id and g.aom_prj_id = f.actv_id and g.deleted = 0
            where f.org_id = #{orgId}
              and g.id = #{xpdId}
              and f.deleted = 0
              and f.user_id = r.user_id
          )

          <include refid="auth_fragment" />
          -- 添加权限控制片段
          group by c.user_id, c.dept_id
          having xIndex &gt; 0
             and yIndex &gt; 0
    )

    <!--查询每个部门下的宫格格子中的落位人数-->
    , DeptCellUserCount as (
        select a.id                      as deptId
             , d.id                      as cellId
             , d.cell_name               as cellName
             , d.cell_name_i18n          as cellNameI18n
             , d.cell_index              as cellIndex
             , d.cell_color              as cellColor
             , count(distinct e.user_id) as cellUserCnt
        from udp_dept               a
        join      udp_dept_relation b on a.id = b.parent_id and b.dept_type = 1 and a.org_id = b.org_id
        join      rv_xpd_grid_cell  d on d.xpd_id = #{xpdId} and d.org_id = #{orgId} and d.deleted = 0 and d.dim_comb_id = #{query.targetId}
        left join UserXYIndex       e on e.xIndex = d.x_index and e.yIndex = d.y_index and b.dept_id = e.dept_id
        where a.org_id = #{orgId}
          and a.deleted = 0

        <include refid="auth_fragment" />

        GROUP BY a.id, d.id, d.cell_name, d.cell_name_i18n, d.cell_index, d.cell_color
    )

    -- 最终查询
    SELECT u.deptId
         , u.deptName
         , u.parentId
         , u.userCnt
         , w.cellId
         , w.cellName
         , w.cellNameI18n
         , w.cellIndex
         , w.cellColor
         , w.cellUserCnt
    FROM DeptUserCount          u
    LEFT JOIN DeptCellUserCount w ON w.deptId = u.deptId;
  </select>

  <select id="findByXpdIdAndUserIdAndDimId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_dim
    where org_id = #{orgId}
    and xpd_id = #{xpdId}
    and user_id = #{userId}
    and sd_dim_id = #{dimId}
    and deleted = 0
  </select>

  <select id="findLevelNum" resultType="com.yxt.talent.rv.controller.manage.xpd.user.dto.XpdUserLevelNumDto">
    select sd_dim_id dimId, grid_level_id gridLevelId, count(user_id) userNum
    from rv_xpd_result_user_dim where org_id = #{orgId} and xpd_id = #{xpdId} and  deleted = 0
    group by sd_dim_id, grid_level_id
  </select>

  <select id="selectIndex4User" resultType="com.yxt.talent.rv.controller.manage.xpd.user.dto.UserCellIndexDTO">
    select a.xpd_id xpdId
         , a.user_id userId
         , max(if(a.sd_dim_id = #{xSdDimId}, b.order_index, -1)) as xIndex
         , max(if(a.sd_dim_id = #{ySdDimId}, b.order_index, -1)) as yIndex
    from rv_xpd_result_user_dim a
          inner join rv_xpd_grid_level b
          on a.grid_level_id = b.id and a.org_id = b.org_id and b.deleted = 0 and a.xpd_id = b.xpd_id
    where a.org_id = #{orgId}
      and a.xpd_id = #{cmd.xpdId}
      and a.user_id = #{cmd.userId}
      and a.sd_dim_id in (#{xSdDimId}, #{ySdDimId})
      and a.deleted = 0
    group by a.xpd_id, a.user_id
    having xIndex &gt; 0
       and yIndex &gt; 0
  </select>

  <select id="getDeptUserCount" resultType="com.yxt.talent.rv.application.xpd.common.dto.DeptUserCountDTO">
    select h.id as deptId, h.name as deptName, h.parent_id as parentId, count(distinct k.user_id) as userCnt
    from udp_dept                         h
    join udp_dept_relation                i on h.id = i.parent_id and i.dept_type = 1 and h.org_id = i.org_id
    join udp_lite_user_sp                 c on c.dept_id = i.dept_id and c.org_id = h.org_id
    join rv_activity_participation_member k on k.user_id = c.user_id and k.deleted = 0
    join rv_xpd                           l on l.org_id = k.org_id and l.aom_prj_id = k.actv_id and l.id = #{xpdId} and l.deleted = 0
    where h.org_id = #{orgId}
      and h.deleted = 0
    <include refid="auth_fragment" />
    group by h.id, h.name, h.parent_id
    having userCnt &gt; 0
  </select>

  <select id="getUserIndexResult" resultType="com.yxt.talent.rv.application.xpd.common.dto.UserXYIndexDTO">
    select c.user_id as userId
         , c.dept_id as deptId
         , max(if(r.sd_dim_id = #{xSdDimId}, t.order_index, -1)) as xIndex
         , max(if(r.sd_dim_id = #{ySdDimId}, t.order_index, -1)) as yIndex
    from rv_xpd_result_user_dim r
    join      udp_lite_user_sp  c on c.user_id = r.user_id
    left join rv_xpd_grid_level t on r.grid_level_id = t.id and t.xpd_id = r.xpd_id and t.deleted = 0
    where r.xpd_id = #{xpdId}
      and r.org_id = #{orgId}
      and r.sd_dim_id in (#{xSdDimId}, #{ySdDimId})
      and r.deleted = 0
      and exists(
        select 1
        from rv_activity_participation_member f
        join rv_xpd                           g on g.org_id = f.org_id and g.aom_prj_id = f.actv_id and g.deleted = 0
        where f.org_id = #{orgId}
          and g.id = #{xpdId}
          and f.deleted = 0
          and f.user_id = r.user_id
      )
    <include refid="auth_fragment" />
    group by c.user_id, c.dept_id
    having xIndex &gt; 0
       and yIndex &gt; 0
  </select>

  <select id="getDeptCellInfo" resultType="com.yxt.talent.rv.application.xpd.common.dto.DeptCellUserCountDTO">
    select a.id                      as deptId
         , b.dept_id                 as subDeptId
         , d.id                      as cellId
         , d.cell_name               as cellName
         , d.cell_name_i18n          as cellNameI18n
         , d.cell_index              as cellIndex
         , d.cell_color              as cellColor
         , d.x_index                 as xIndex
         , d.y_index                 as yIndex
    from udp_dept               a
    join      udp_dept_relation b on a.id = b.parent_id and b.dept_type = 1 and a.org_id = b.org_id
    join      rv_xpd_grid_cell  d on d.xpd_id = #{xpdId} and d.org_id = #{orgId} and d.deleted = 0 and (d.dim_comb_id = #{dimCombId} or d.dim_comb_id = '')
    where a.org_id = #{orgId}
      and a.deleted = 0

    <if test="query.cellIds != null and query.cellIds.size() != 0">
      AND d.id in
      <foreach close=")" collection="query.cellIds" item="itemId" open="(" separator=",">
        #{itemId}
      </foreach>
    </if>
  </select>

  <select id="findByXpdIdAndUserIds"
          resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserDimPO">
    select
      rurd.id, rurd.org_id, rurd.xpd_id, rurd.user_id, rurd.sd_dim_id, rurd.grid_level_id, rurd.score_value, rurd.qualified_ptg,
      rurd.deleted, rurd.create_user_id, rurd.create_time, rurd.update_user_id, rurd.update_time, rurd.calc_batch_no,
      rurd.perf_result_id, rurd.cali_flag, rurd.original_snap,
      gl.level_name as gridLevelName,
      gl.level_name_i18n as gridLevelNameI18n,
      gl.third_dim_color as thirdDimColor,
      gl.order_index as gridLevelOrderIndex
    from rv_xpd_result_user_dim rurd
    left join rv_xpd_grid_level gl on rurd.grid_level_id = gl.id
    where rurd.org_id = #{orgId}
      and rurd.xpd_id = #{xpdId}
      and rurd.user_id in
    <foreach close=")" collection="userIds" index="index" item="userId" open="(" separator=",">
      #{userId}
    </foreach>
    and rurd.deleted = 0
  </select>

  <select id="findUser" resultType="com.yxt.talent.rv.controller.manage.xpd.user.dto.ViewUserDimDTO">
    select
      sd_dim_id dimId,
      user_id userId,
      score_value scoreValue,
      qualified_ptg qualifiedPtg
    from rv_xpd_result_user_dim
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </select>

</mapper>